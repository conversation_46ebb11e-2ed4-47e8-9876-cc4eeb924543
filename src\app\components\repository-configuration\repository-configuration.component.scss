@import "../../../_variables";
@import "../../../assets/dist/css/font.scss";

.child-component{
  border: 1px solid $Neutral-Gray-10; 
  border-radius: $space-4;
}
.repository-config-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .header-container {    
    .tab-container {
      flex-grow: 1;
      
      .repository-tabs {
          .nep-tabs-header-tabs {
            border-bottom: none;
          }
      }
    }    
    .button-container {
      .email-config-btn {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        font-size: $nep-subtitle-fontsize;
      }
    }
  }
  
  .content-container {
    height: calc(100% - 50px);    
    .email-notification-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 400px;
      border:$border-color-dark;
      border-radius:$space-4;
    }
  }
  .email-config-btn {
    transition: all 0.3s ease;
  }
  .email-config-btn img {
    transition: filter 0.3s ease;
  }
  .email-config-btn:hover img {
    filter: brightness(0) invert(1);
  }
}

.content-height{
  height: calc(100vh - 200px);
}