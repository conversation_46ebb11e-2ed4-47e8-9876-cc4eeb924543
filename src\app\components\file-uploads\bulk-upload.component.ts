import { Component, Inject, ViewChild } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from "@angular/platform-browser";
import { Message } from "primeng/api/message";
import { MessageService } from "primeng/api";
import { FileUploadService } from "../../services/file-upload.service";
import { UploadService } from "../../services/upload.service";
import {
  MiscellaneousService,
  ErrorMessage,
  AdhocPeriodType,
} from "../../services/miscellaneous.service";
import { PortfolioCompanyService } from "../../services/portfolioCompany.service";
import {
  ActionsEnum,
  PermissionService,
  UserSubFeaturesEnum,
  KPIModulesEnum,
  FeaturesEnum
} from "../../services/permission.service";
import { FundService } from "../../services/funds.service";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { Bul<PERSON><PERSON>load<PERSON>onstant<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/app/common/constants";
import { InvestorService } from "src/app/services/investor.service";
import { HttpClient } from "@angular/common/http";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { FormatSettings } from "@progress/kendo-angular-dateinputs";
@Component({
  selector: "bulk-upload",
  templateUrl: "./bulk-upload.component.html",
  providers: [MessageService, FileUploadService],
  styleUrls: ["./bulk-upload.component.scss"],
})
export class BulkUploadComponent {
  public format: FormatSettings = {
    displayFormat: "MM/yyyy",
    inputFormat: "MM/yyyy"
  };
  public virtual: any = {
    itemHeight: 34,
    pageSize: 20
  };
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains',
  };
  public tagMapper(tags: any[]): any[] {
    return tags.length < 1 ? tags : [tags];
  }
  kpiModuleAlias: typeof KpiModuleAlias = KpiModuleAlias;
  kPIModulesEnum: typeof KPIModulesEnum = KPIModulesEnum;
  uploadedFiles: any[] = [];
  messages: any[] = [];
  errormessages: any[] = [];
  progress: number;
  messageClass: string = "bulkMessage";
  msgTimeSpan: number;
  safeHtml: SafeHtml;
  loading: boolean;
  moduleDetails: any = {};
  masterModel: any = {};
  model: any = {};
  moduleName: string = "";
  @ViewChild("fileUploader") fileUploader: any = {};
  showClickMessage: boolean;
  TemplateFileName: string = "";
  value: number = 0;
  cancel: boolean = false;
  interval: any = 0;
  ProgressCancel: boolean = true;
  showCancelButton: boolean = true;
  FileProgresStatus: string = "Cancel File Progress";
  uploadResultobj: { [k: string]: any } = {};
  msgs: Message[] = [];
  strModuleType: string = "";
  strAPIURL: string = "";
  PorfolioCompanies: any = [];
  PortfolioCompanyId: number;
  FundsList: any = [];
  FundId: number;
  encryptedFundId: any;
  investorId: any;
  encryptInvestorId: any;
  fundDropDownDisabled: boolean = true;
  isFundModuleSelected: boolean = false;
  isConditionalDropDown: string = "common";
  IsValidFundName: boolean = true;
  IsValidInvestor: boolean = true;
  CompanyDisabled: boolean = true;
  IsValidCompany: boolean = false;
  EnableDownload: boolean = true;
  disableDownload: boolean = false;
  hideUnauthorized: boolean = true;
  showErrorPopUp: boolean = false;
  canImport: boolean = true;
  featureName: any;
  encryptedPortfolioCompanyID: any;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  defaultPlaceholder = "Browse";
  uploadFilePlaceholder = this.defaultPlaceholder;
  browseicon = true;
  files = [];
  investorList = [];
  popUpErrorMsg: any = "";
  portfolioCompData = {};
  fundData = {};
  investorData = {};
  adhocPeriod = { type: "" };
  yearRange: any;
  adhocPeriodTypes = [];
  adhocMonth: string = "";
  adhocSelectedMonth: string = "";
  adhocQuarter: string = "";
  adhocYear: string = "";
  adhocModelYear: string = "";
  yearOptions: any = [];
  isPeriod: boolean = false;
  isPeriodValidate: boolean = false;
  isQuarterValidate: boolean = false;
  isMonthValidate: boolean = false;
  isYearValidate: boolean = false;
  isLoading: boolean = false;
  allfiletypes: Array<string> = [];

  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  errorCount = 0;
  fundList = [];
  fundOfFund = "Fund";
  fofTemplateFileName = "FundList_Import";
  fOFAliasName = "FOF";
  myAppUrl: string;
  isNewTemplate: boolean = false;
  showAllFileUploadPopUp: boolean = false;
  isFundCheckAll: boolean = false;
  debounceTimeout: any;
  isFundKpis: boolean = false;
  selectedFundId: number = 0;
  selectedFundName: string = "";
  constructor(
    private messageService: MessageService,
    private http: HttpClient,
    private sanitizer: DomSanitizer,
    private miscService: MiscellaneousService,
    private fileUploadService: FileUploadService,
    private portfolioCompanyService: PortfolioCompanyService,
    private permissionService: PermissionService,
    private uploadService: UploadService,
    private _fundService: FundService,
    private toastrService: ToastrService,
    private _investorService: InvestorService,
    @Inject("BASE_URL") baseUrl: string
  ) {
    this.myAppUrl = baseUrl;
    this.msgTimeSpan = this.miscService.getMessageTimeSpan();
    this.adhocPeriodTypes = [
      { type: AdhocPeriodType.Monthly },
      { type: AdhocPeriodType.Quarterly },
      { type: AdhocPeriodType.Annual },
    ];
    this.yearOptions = this.miscService.bindYearList();
    this.yearRange = "2000:" + new Date().getFullYear();
  }
  /**
   * Cancels the file upload event.
   */
  cancelButtonEvent() {
    this.showAllFileUploadPopUp = false;
  }
  public onSelectAllClick() {
    this.isFundCheckAll = !this.isFundCheckAll;
    this.fundList = this.isFundCheckAll ? this.FundsList : [];
    this.onFundChangeModel(this.fundList);
  }
  isIndet() {
    let isIndet = this.fundList?.length !== 0 && this.fundList?.length !== this.FundsList?.length; 
    if(this.isFundCheckAll != isIndet) {
      this.isFundCheckAll = !isIndet;
    }
    return isIndet;
  }
  /**
   * Handles the submit button event for bulk upload.
   * @param results - The results of the bulk upload.
   */
  onSubmitButtonEvent(results: any) {
    this.showAllFileUploadPopUp = false;
    let num = 0;
    this.errormessages = [];
    this.messageClass = "";
    if (this.moduleName == ModuleList.ESG) {
      this.uploadService.notifyUploadCompleted();
    }
    else {
      for (let result of results) {
        if (
          result.code != null &&
          result.code.trim().toLowerCase() == BulkuploadConstants.ok
        ) {
          this.toastrService.success(
            BulkuploadConstants.FileUploadedSuccessfully,
            "",
            {
              positionClass: BulkuploadConstants.ToasterMessagePossition,
            }
          );
        } else {
          this.fileUploadFailedEvent(result, num, results);
        }
      }
    }
  }
  /**
   * Handles the event when a file upload fails.
   * @param result - The result of the file upload.
   * @param num - The number of the file upload.
   * @param results - The results of all file uploads.
   */
  fileUploadFailedEvent(result: any, num: number, results: any) {
    if (result?.message || result.excelStatuses?.length > 0) {
      if (num === 0 && !result.message) {
        result.message = BulkuploadConstants.InvalidateDataorSheet;
      }
      num++;
      if (result?.excelStatuses?.length > 0) {
        this.processExcelStatuses(result);
      } else {
        this.processErrorMessage(result, results);
      }

      this.getErrorCount();
      this.messageClass = "errorMessage";
      this.safeHtml = result.message;
      this.uploadResultobj.messageClass = this.messageClass;
      this.uploadResultobj.safeHtml = this.safeHtml;
      this.messages.push(this.uploadResultobj);

      this.toastrService.error(BulkuploadConstants.ErrorFoundSelectedFile, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    }
  }
  /**
   * Processes the excel statuses and updates the result message and error messages accordingly.
   * @param result - The result object containing excel statuses.
   */
  processExcelStatuses(result: any) {
    result?.excelStatuses?.forEach((excelStatus) => {
      let errors = excelStatus;
      let message = `${errors?.sheetName}`;
      result.message =
        result?.message != null && result?.message != undefined
          ? `${result?.message} ${message}`
          : message;
      result.message = result?.message?.replace("null", "");
      errors?.excelCodeStatus?.forEach((status) => {
        let statusMessage = `${
          status.cellCode != null ? status.cellCode : "-"
        } -  ${status?.message}`;
        let errMsgs = [];
        let res = this.errormessages.find(
          (x) =>
            x.sheetname != "" &&
            x.sheetname.toString().toLowerCase() ==
              errors.sheetName.toString().toLowerCase()
        );
        if (res != undefined) {
          errMsgs = res.messages;
          this.errormessages = this.errormessages.filter(
            (x) =>
              x.sheetname.toString().toLowerCase() !=
              errors.sheetName.toString().toLowerCase()
          );
        }
        let errorRes = {
          sheetname: errors.sheetName,
          messages: [
            ...errMsgs,
            {
              cellCode: status.cellCode != null ? status.cellCode : "-",
              message: status?.message,
            },
          ],
          isExpanded: true,
        };
        this.errormessages.push(errorRes);
        result.message =
          result.message != null && result.message != undefined
            ? `${result.message} ${statusMessage}`
            : statusMessage;
      });
    });
  }

  /**
   * Processes the error message and adds it to the error messages array.
   * @param result - The result object containing the error message.
   * @param results - The results object containing additional information.
   */
  processErrorMessage(result: any, results: any) {
    let msgs = result.message.toString().trim().split("<br/>");
    msgs = msgs.filter((x) => x != "");
    let errorRes = {
      sheetname: results?.sheetName,
      messages: msgs,
      isExpanded: true,
    };
    this.errormessages.push(errorRes);
  }
  /**
   * Calculates the total count of errors in the error messages.
   * @returns The total count of errors.
   */
  getErrorCount() {
    let errorCount = 0;
    this.errormessages.forEach((element) => {
      errorCount = errorCount + element.messages.length;
    });
    this.errorCount = errorCount;
  }
  /**
   * Handles the toaster message event.
   * @param event - The event object containing the message and type.
   */
  toasterMessageEvent(event: { message: string; type: string }) {
    if (event.type == "success") {
      this.toastrService.success(event.message, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    } else if (event.type == "warning") {
      this.toastrService.warning(event.message, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    } else {
      this.toastrService.error(event.message, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    }
  }
  /**
   * Initializes the component.
   * Sets up the toastr overlay container, retrieves module list, companies, fund list,
   * investor dropdown list, and sets a flag in local storage.
   */
  ngOnInit() {
    this.toastrService.overlayContainer = this.toastContainer;
    this.getModuleList();
    this.getCompanies();
    this.getFundList();
    this.getInvestorDropdownList();
    localStorage.setItem("BulkUpload", "true");
  }
  getFormattedDate(date: any) {
    return date == 'NA' || date == undefined || date == null || date == '' ? null : new Date(date);
 }
  /**
   * Sets the selected quarter and year for the adhocQuarter property.
   * @param event - The event object containing the selected quarter and year.
   */
  fromQuarterYear(event: any) {
    this.isQuarterValidate = false;
    this.adhocQuarter = event.quarter + " " + event.year;
  }
  /**
   * Handles the selection of a month.
   * @param {any} event - The event object containing the selected month.
   */
  onSelectMonth(event) {
    this.isMonthValidate = false;
    let d = new Date(Date.parse(event));
    this.adhocSelectedMonth = `${d.getMonth() + 1}/${d.getFullYear()}`;
  }
  /**
   * Handles the change event of the year options.
   * @param event - The event object containing the selected value.
   */
  yearOptionsChange(event) {
    this.isYearValidate = false;
    this.adhocYear = event.value;
  }
  /**
   * Clears the data in the component.
   */
  clearData() {
    this.adhocPeriod = { type: "" };
    this.adhocQuarter = "";
    this.adhocYear = "";
    this.adhocSelectedMonth = "";
    this.adhocMonth = "";
    this.adhocModelYear = "";
  }
  /**
   * Validates the selected period and updates the corresponding properties based on the selected period type.
   * @param event The event object containing the selected period.
   */
  validateSelectedPeriod(event) {
    this.isPeriodValidate = false;

    switch (event.type) {
      case AdhocPeriodType.Monthly:
        this.adhocQuarter = "";
        this.adhocYear = "";
        this.adhocModelYear = "";
        break;

      case AdhocPeriodType.Quarterly:
        this.adhocMonth = "";
        this.adhocYear = "";
        this.adhocModelYear = "";
        this.adhocSelectedMonth = "";
        break;

      case AdhocPeriodType.Annual:
        this.adhocMonth = "";
        this.adhocSelectedMonth = "";
        this.adhocQuarter = "";
        break;
    }
  }
  /**
   * Retrieves the investor dropdown list.
   */
  getInvestorDropdownList() {
    this._investorService.getInvestorList({}).subscribe((result: any) => {
      this.investorList = result;
    });
  }

  /**
   * Toggles the expansion state of an item and updates the error messages accordingly.
   * @param item - The item to expand or collapse.
   */
  isExpand(item) {
    item.isExpanded = !item?.isExpanded;
    this.errormessages = this.errormessages.map((y) => ({
      ...y,
      isExpanded: y.sheetname === item.sheetname?item.isExpanded:y.isExpanded,
    }));
  }
  /**
   * Retrieves the portfolio companies from the portfolioCompanyService.
   * Updates the PorfolioCompanies property with the result.
   */
  getCompanies() {
    this.portfolioCompanyService
      .getPortfolioCompany()
      .subscribe((result: any) => {
        this.PorfolioCompanies = result;
      });
  }
  /**
   * Uploads a file.
   * @param file - The file to be uploaded.
   */
  private uploadFiles(file: any) {
    this.loading = true;
    let local = this;
    this.uploadResultobj = {};
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    file.showCancelButton = true;
    this.ProgressCancel = false;
    this.safeHtml = "";
    let ModuleName = this.moduleName;
    let strAPIURL = this.strAPIURL;
    if(this.moduleName == BulkuploadConstants.MonthlyReportModule && !BulkuploadConstants.fileExtensions.some(extension => file?.name.endsWith(extension))){
      this.loading = false;
      this.ProgressCancel = true;
      this.toastrService.error(BulkuploadConstants.ValidateExcelFormat, "", { positionClass: BulkuploadConstants.ToasterMessagePossition,});
       return;
     }
    if (file.length === 0) {
      this.safeHtml = "Error :- No file selected. Please select a file.";
      this.messageClass = "errorMessage";
      this.ProgressCancel = true;
      return;
    }
    if(this.moduleName == BulkuploadConstants.MonthlyReportModule && this.adhocSelectedMonth == "" ){
      this.isMonthValidate = true;
      this.loading = false;
      this.ProgressCancel = true;
      return;
    }
    if (this.moduleName == "adhoc" && this.adhocPeriod.type == "") {
      this.isPeriodValidate = true;
      this.loading = false;
      this.ProgressCancel = true;
      return;
    }
    if (
      this.moduleName == "adhoc" &&
      this.adhocPeriod.type == AdhocPeriodType.Monthly &&
      this.adhocSelectedMonth == ""
    ) {
      this.loading = false;
      this.ProgressCancel = true;
      this.isMonthValidate = this.adhocQuarter == "" ? true : false;
      return;
    }
    if (
      this.moduleName == "adhoc" &&
      this.adhocPeriod.type == AdhocPeriodType.Quarterly &&
      this.adhocQuarter == ""
    ) {
      this.loading = false;
      this.ProgressCancel = true;
      this.isQuarterValidate = this.adhocQuarter == "" ? true : false;
      return;
    }
    if (
      this.moduleName == "adhoc" &&
      this.adhocPeriod.type == AdhocPeriodType.Annual &&
      this.adhocYear == ""
    ) {
      this.loading = false;
      this.ProgressCancel = true;
      this.isYearValidate = this.adhocYear == "" ? true : false;
      return;
    }
    try {
      const formData = new FormData();
      formData.append("ModuleName", ModuleName);
      formData.append(file.name, file);
      if (local.PortfolioCompanyId !== 0) {
        let encryptedPortfolioCompanyID = local.PorfolioCompanies.filter(
          (s) => s.portfolioCompanyID === local.PortfolioCompanyId
        )[0].encryptedPortfolioCompanyId;
        formData.append("PortfolioCompanyID", encryptedPortfolioCompanyID);
      } else if (local.FundId != 0)
        formData.append("FundId", local.encryptedFundId);
      else if (local.investorId != 0) {
        formData.append("InvestorId", local.encryptInvestorId);
        this.cancel = false;
      }
      if (this.moduleName == "adhoc") {
        if (this.adhocYear != "") formData.append("Year", this.adhocYear);
        if (this.adhocSelectedMonth != "")
          formData.append("Month", this.adhocSelectedMonth);
        if (this.adhocQuarter != "")
          formData.append("Quarter", this.adhocQuarter);
      }
      if (this.moduleName == "esg") {
        this.prepareFilesList(file);
      }
      if (
        this.moduleName?.toLocaleLowerCase() ==
        this.fundOfFund?.toLocaleLowerCase()
      ) {
        this.prepareFoFFilesList(file);
      }
      if (
        !this.cancel &&
        this.moduleName != "esg" &&
        this.moduleName != this.fundOfFund
      ) {
        this.cancel = true;
        this.FileProgresStatus = "File Processing...";
        if(this.moduleName == BulkuploadConstants.MonthlyReportModule && this.adhocSelectedMonth != ""){
          let subPageId = 40;
          formData.append(BulkuploadConstants.Template, file);
          formData.append(BulkuploadConstants.SubPageId, String(subPageId));
          formData.append("MonthlyReportPeriod", String(this.adhocSelectedMonth));
          formData.append(BulkuploadConstants.CompanyId, String(local.PortfolioCompanyId));
          this.fileUploadService.OnSubmitAllFilesUpload(formData).subscribe({
            next: (result) => {
              this.clearResponse()
              this.onSubmitButtonEvent(result);
              if(result.code == "ok")
              this.toastrService.success("File uploaded successfully", "", {
                positionClass: BulkuploadConstants.ToasterMessagePossition,
              });
            },
            error: (error) => {
              this.setErrors();
            },
          });
        } else{
          this.fileUploadService.importBulkData(formData, strAPIURL).subscribe({
            next: (results) => {
              this.loading = false;
              let num = 0;
              this.errormessages = [];
              this.messageClass = "";
              let iterations = results["body"];
              for (let result of results["body"]) {
                try {
                  this.uploadResultobj = {};
                  clearInterval(this.interval);
                  setInterval(() => {
                    this.ProgressCancel = true;
                  }, 2000);
                  if (
                    result.code != null &&
                    result.code.trim().toLowerCase() == "ok"
                  ) {
                    this.messageClass = "bulkMessage";
  
                    this.uploadedFiles.push(file);
  
                    this.uploadResultobj.messageClass = this.messageClass;
                    this.uploadResultobj.safeHtml = this.safeHtml;
                    this.messages.push(this.uploadResultobj);
                    this.files = [];
                    this.uploadFilePlaceholder = this.defaultPlaceholder;
                    this.browseicon = true;
                    this.toastrService.success("File uploaded successfully", "", {
                      positionClass: BulkuploadConstants.ToasterMessagePossition,
                    });
                  } else if (
                    result.code != null &&
                    result.code.trim().toLowerCase() == "info"
                  ) {
                    this.messageClass = "infoMessage";
                    this.safeHtml = result.message;
                    this.uploadResultobj.messageClass = this.messageClass;
                    this.uploadResultobj.safeHtml = this.safeHtml;
                    this.messages.push(this.uploadResultobj);
                  } else {
                    if (ModuleName != "adhoc") {
                      if (
                        (result.message != null && result.message != "") ||
                        result?.excelStatuses.length > 0
                      ) {
                        if (
                          num == 0 &&
                          (result.message == undefined ||
                            result.message == null ||
                            result.message == "")
                        ) {
                          result.message =
                            "One or more records in the file has invalid data or sheet(s) may be empty. Please upload the file again after correction.";
                        }
                        num = num + 1;
                        if (
                          result?.excelStatuses != undefined &&
                          result?.excelStatuses.length > 0
                        ) {
                          result?.excelStatuses?.forEach((excelStatus) => {
                            let errors = excelStatus;
                            let message = `${errors?.sheetName}`;
                            result.message =
                              result?.message != null &&
                              result?.message != undefined
                                ? `${result?.message} ${message}`
                                : message;
                            result.message = result?.message?.replace("null", "");
                            errors?.excelCodeStatus?.forEach((status) => {
                              let statusMessage = `${
                                status.cellCode != null ? status.cellCode : ""
                              } -  ${status?.message}`;
                              let errMsgs = [];
                              let res = this.errormessages.find(
                                (x) =>
                                  x.sheetname != "" &&
                                  x.sheetname.toString().toLowerCase() ==
                                    errors.sheetName.toString().toLowerCase()
                              );
                              if (res != undefined) {
                                errMsgs = res.messages;
                                this.errormessages = this.errormessages.filter(
                                  (x) =>
                                    x.sheetname.toString().toLowerCase() !=
                                    errors.sheetName.toString().toLowerCase()
                                );
                              }
                              let errorRes = {
                                sheetname: errors.sheetName,
                                messages: [
                                  ...errMsgs,
                                  {
                                    cellCode: status.cellCode,
                                    message: status?.message,
                                  },
                                ],
                                isExpanded: false,
                              };
                              this.errormessages.push(errorRes);
                              result.message =
                                result.message != null &&
                                result.message != undefined
                                  ? `${result.message} ${statusMessage}`
                                  : statusMessage;
                            });
                          });
                        } else {
                          let msgs = result.message
                            .toString()
                            .trim()
                            .split("<br/>");
                          msgs = msgs.filter((x) => x != "");
                          let errorRes = {
                            sheetname: results?.sheetName,
                            messages: msgs,
                            isExpanded: false,
                          };
                          this.errormessages.push(errorRes);
                        }
  
                        this.messageClass = "errorMessage";
                        this.safeHtml = result.message;
                        this.uploadResultobj.messageClass = this.messageClass;
                        this.uploadResultobj.safeHtml = this.safeHtml;
                        this.messages.push(this.uploadResultobj);
                        this.toastrService.error(
                          "Errors found in selected file",
                          "",
                          {
                            positionClass:
                              BulkuploadConstants.ToasterMessagePossition,
                          }
                        );
                      }
                    } else {
                      if (result != undefined && result != "") {
                        this.messageClass = "errorMessage";
                        let message = `<b>${result?.sheetName}:</b></br>`;
                        result.message =
                          result?.message != null && result?.message != undefined
                            ? `${result?.message} ${message}`
                            : message;
                        result.message = result?.message?.replace("null", "");
                        result?.excelCodeStatus.forEach((status) => {
                          let statusMessage = `<b>${
                            status.cellCode != null ? status.cellCode : ""
                          }</b> -  ${status?.message}</br>`;
                          result.message =
                            result.message != null && result.message != undefined
                              ? `${result.message} ${statusMessage}`
                              : statusMessage;
                          this.messageClass =
                            status.code == "success" ? "success" : "errorMessage";
                          let errMsgs = [];
                          let res = this.errormessages.find(
                            (x) =>
                              x.sheetname != "" &&
                              x.sheetname.toString().toLowerCase() ==
                                result.sheetName.toString().toLowerCase()
                          );
                          if (res != undefined) {
                            errMsgs = res.messages;
                            this.errormessages = this.errormessages.filter(
                              (x) =>
                                x.sheetname.toString().toLowerCase() !=
                                result.sheetName.toString().toLowerCase()
                            );
                          }
                          let errorRes = {
                            sheetname: result.sheetName,
                            messages: [
                              ...errMsgs,
                              {
                                cellCode: status.cellCode,
                                message: status?.message,
                              },
                            ],
                            isExpanded: false,
                          };
                          this.errormessages.push(errorRes);
                          if (this.messageClass == "success" && !--iterations) {
                            this.files = [];
                            this.uploadFilePlaceholder = this.defaultPlaceholder;
                            this.browseicon = true;
                            this.toastrService.success(
                              "File uploaded successfully",
                              "",
                              {
                                positionClass:
                                  BulkuploadConstants.ToasterMessagePossition,
                              }
                            );
                          }
                        });
                      }
  
                      this.uploadResultobj.messageClass = this.messageClass;
                      this.uploadResultobj.safeHtml = this.safeHtml;
                      this.messages.push(this.uploadResultobj);
                    }
                  }
                } catch (e) {
                  this.messageClass = "errorMessage";
                  this.messageService.add({
                    severity: "error",
                    summary: "Error",
                    detail: "Please check the file",
                  });
                  this.value = 100;
                  setInterval(() => {
                    this.ProgressCancel = true;
                  }, 2000);
                  this.toastrService.error("Errors found in selected file", "", {
                    positionClass: BulkuploadConstants.ToasterMessagePossition,
                  });
                }
              }
  
              //errorCount
              let errorCount = 0;
              this.errormessages.forEach((element) => {
                errorCount = errorCount + element.messages.length;
              });
              this.errorCount = errorCount;
            },
            error: (error) => {
              this.loading = false;
              if (this.moduleName != "esg") {
                this.toastrService.error("Errors found in selected file", "", {
                  positionClass: BulkuploadConstants.ToasterMessagePossition,
                });
              }
              this.ProgressCancel = false;
            },
          });
        }
      }
    } catch (e) {
      this.loading = false;
      this.messageService.add({
        severity: "error",
        summary: "Error",
        detail: "Please check the file",
      });
      this.messageClass = "errorMessage";
    }
  }
  /**
   * Retrieves the bulk modules from the misc service.
   * Updates the moduleList in the masterModel if the result is not null and has a length greater than 0.
   */
  getBulkModules() {
    this.miscService.getBulkModules().subscribe({
      next: (result) => {
        if (result != null && result.length > 0) {
          this.masterModel.moduleList = result;
        }
      },
      error: (error) => {},
    });
  }
  modulesLoading: boolean;
  /**
   * Retrieves the module list and updates the selected module details.
   */
  getModuleList() {
    this.modulesLoading = true;
    let localModel = this.model;
    this.getBulkModules();
    if (
      this.model.moduleDetails != null &&
      this.model.moduleDetails.moduleId > 0
    ) {
      this.model.moduleDetails = this.masterModel.moduleList.filter(function (
        element: any,
        index: any
      ) {
        return element.moduleId == localModel.moduleDetails.moduleId;
      })[0];
    }
    this.modulesLoading = false;
  }

  /**
   * Sets the dropdown value and performs necessary actions based on the selected module.
   *
   * @param f - The selected module object.
   */
  getDropdownValue = (f: any) => {
    this.isNewTemplate = false;
    this.portfolioCompData = null;
    this.uploadFilePlaceholder = this.defaultPlaceholder;
    this.browseicon = true;
    this.files = [];
    this.PortfolioCompanyId = 0;
    this.FundId = 0;
    this.investorId = 0;
    this.fundList = [];
    this.fundData = null;
    this.adhocSelectedMonth="";
    this.adhocMonth="";
    if (f.alias == "") this.hideUnauthorized = true;
    else this.hideUnauthorized = false;

    this.featureName = f.alias;
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    this.CompanyDisabled = true;
    this.IsValidCompany = false;
    this.showClickMessage = true;
    this.isFundModuleSelected = false;
    this.isConditionalDropDown = "common";
    this.moduleName = f.moduleName.toString()?.trim()?.toLowerCase();
    this.isFundKpis = false;
    this.selectedFundId = 0;
    this.selectedFundName = "";
    switch (this.moduleName) {
      case ModuleList.User:
        this.TemplateFileName = "UserList_Import";
        this.strModuleType = "UserList";
        this.strAPIURL = "api/user/import/" + false;
        break;
      case ModuleList.Firm:
        this.TemplateFileName = "FirmList_Import";
        this.strModuleType = "FirmList";
        this.strAPIURL = "api/firm/import";
        break;
      case ModuleList.Deal:
        this.TemplateFileName = "DealList_Import";
        this.strModuleType = "DealList";
        this.strAPIURL = "api/deals/import";
        break;
      case ModuleList.Fund:
        this.TemplateFileName = this.fofTemplateFileName;
        this.strModuleType = this.fundOfFund;
        this.strAPIURL = "api/fof/import/template";
        this.isFundModuleSelected = true;
        this.isConditionalDropDown = this.fOFAliasName;
        this.IsValidFundName = false;
        this.hideUnauthorized = false;
        break;
      case ModuleList.FundFinancials:
        this.isNewTemplate = true;
        this.TemplateFileName = "FundFinancials_Import";
        this.strModuleType = "FundFinancials";
        this.strAPIURL = "api/bulkupload/import";
        this.isFundKpis = true;
        this.isConditionalDropDown = "fundKpis";
        break;
      case ModuleList.FundKpis:
        this.isFundKpis = true;
        this.isNewTemplate = true;
        this.TemplateFileName = "FundKpis_Import";
        this.strModuleType = "FundKpis";
        this.strAPIURL = "api/bulkupload/import";
        this.isConditionalDropDown = "fundKpis";
        break;
      case ModuleList.Company:
        this.TemplateFileName = "PortfolioCompany_Import";
        this.strModuleType = "PortFolioCompany";
        this.strAPIURL = "api/portfolio-company/import";
        break;
      case ModuleList.CompanyKpi:
        this.isNewTemplate = true;
        this.TemplateFileName = "CompanyKPI_Import";
        this.strModuleType = "CompanyKPI";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.Financials:
        this.isNewTemplate = true;
        this.TemplateFileName = "Financials_Import";
        this.strModuleType = "Financials";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.ImpactKpi:
        this.isNewTemplate = true;
        this.TemplateFileName = "ImpactKPI_Import";
        this.strModuleType = "ImpactKPI";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.InvestmentKpi:
        this.isNewTemplate = true;
        this.TemplateFileName = "InvestmentKPI_Import";
        this.strModuleType = "InvestmentKPI";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.MonthlyReport:
        this.TemplateFileName = "MonthlyReport_Import";
        this.strModuleType = "MonthlyReport";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        this.adhocPeriod.type = "";
        break;
      case ModuleList.OperationalKpi:
        this.isNewTemplate = true;
        this.TemplateFileName = "OperationalKPI_Import";
        this.strModuleType = "OperationalKPI";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.ExchangeRates:
        this.TemplateFileName = "ExchangeRates_Import";
        this.strModuleType = "ExchangeRates";
        this.strAPIURL = "api/bulkupload/import";
        break;
      case ModuleList.TradingRecords:
        this.isNewTemplate = true;
        this.TemplateFileName = "TradingRecords_Import";
        this.strModuleType = "TradingRecords";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.CreditKpi:
        this.isNewTemplate = true;
        this.TemplateFileName = "CreditKPI_Import";
        this.strModuleType = "CreditKPI";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.ValuationTable:
        this.TemplateFileName = "Valuation_Data";
        this.strModuleType = "ValuationData";
        this.strAPIURL = "investor/import";
        this.isConditionalDropDown = "investor";
        this.IsValidInvestor = false;
        this.hideUnauthorized = false;
        break;
      case ModuleList.Adhoc:
        this.TemplateFileName = "Adhoc_Import";
        this.strModuleType = "Adhoc";
        this.CompanyDisabled = false;
        this.strAPIURL = "api/UnstructuredHistory/UploadUnstructuredFile";
        break;
      case ModuleList.Investor:
        this.TemplateFileName = "Investor_Investment_Summary";
        this.strModuleType = "Investor_Investment_Summary";
        this.strAPIURL = "investor-funds/import";
        this.isConditionalDropDown = "investor";
        this.IsValidInvestor = false;
        this.hideUnauthorized = false;
        break;
      case ModuleList.ESG:
        this.isNewTemplate = true;
        this.TemplateFileName = "";
        this.strModuleType = "ESG";
        this.strAPIURL = "api/esg/upload/template";
        this.CompanyDisabled = false;
        break;
      case ModuleList.CapTable:
        this.isNewTemplate = true;
        this.TemplateFileName = "";
        this.strModuleType = "CapTable";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.OtherCapTable:
        this.isNewTemplate = true;
        this.TemplateFileName = "";
        this.strModuleType = "OtherCapTable";
        this.strAPIURL = "api/bulkupload/import";
        this.CompanyDisabled = false;
        break;
      case ModuleList.CustomTable1:
          this.setModuleConfig("CustomTable1", "CustomTable1_Import");
        break;
      case ModuleList.CustomTable2:
          this.setModuleConfig("CustomTable2", "CustomTable2_Import");
        break;
      case ModuleList.CustomTable3:
          this.setModuleConfig("CustomTable3", "CustomTable3_Import");
        break;
      case ModuleList.CustomTable4:
          this.setModuleConfig("CustomTable4", "CustomTable4_Import");
        break;
      case ModuleList.OtherKPI1:
          this.setModuleConfig("OtherKPI1", "OtherKPI1_Import");
        break;
      case ModuleList.OtherKPI2:
          this.setModuleConfig("OtherKPI2", "OtherKPI2_Import");
        break;
      case ModuleList.OtherKPI3:
          this.setModuleConfig("OtherKPI3", "OtherKPI3_Import");
        break;
      case ModuleList.OtherKPI4:
          this.setModuleConfig("OtherKPI4", "OtherKPI4_Import");
        break;
      case ModuleList.OtherKPI5:
          this.setModuleConfig("OtherKPI5", "OtherKPI5_Import");
        break;
      case ModuleList.OtherKPI6:
          this.setModuleConfig("OtherKPI6", "OtherKPI6_Import");
        break;
      case ModuleList.OtherKPI7:
          this.setModuleConfig("OtherKPI7", "OtherKPI7_Import");
        break;
      case ModuleList.OtherKPI8:
          this.setModuleConfig("OtherKPI8", "OtherKPI8_Import");
        break;
      case ModuleList.OtherKPI9:
          this.setModuleConfig("OtherKPI9", "OtherKPI9_Import");
        break;
      case ModuleList.OtherKPI10:
          this.setModuleConfig("OtherKPI10", "OtherKPI10_Import");
        break;
    }

    if (this.fileUploader !== undefined) {
      this.fileUploader.files = [];
    }
    this.safeHtml = "";
    this.value = 0;
    clearInterval(this.interval);
    this.ProgressCancel = true;
  };

  /**
   * Sets the module configuration based on the provided module type and template file name.
   * @param moduleType - The module type.
   * @param templateFileName - The template file name.
   */
  setModuleConfig(moduleType: string, templateFileName: string) {
    this.isNewTemplate = true;
    this.TemplateFileName = templateFileName;
    this.strModuleType = moduleType;
    this.strAPIURL = "api/bulkupload/import";
    this.CompanyDisabled = false;
  } 
  /**
   * Retrieves the fund list by calling the getFundData method of the _fundService.
   * If the result is not null and has a length greater than 0, it assigns the result to the FundsList property.
   * Otherwise, it displays an error message using the toastrService.
   */
  getFundList() {
    this._fundService.getFundData().subscribe({
      next: (result) => {
        if (result != null && result.length > 0) {
          this.FundsList = result;
        } else {
          this.toastrService.error(ErrorMessage.SomethingWentWrong, "", {
            positionClass: BulkuploadConstants.ToasterMessagePossition,
          });
        }
      },
      error: (error) => {
        this.toastrService.error(ErrorMessage.SomethingWentWrong, "", {
          positionClass: BulkuploadConstants.ToasterMessagePossition,
        });
      },
    });
  }
  /**
   * Handles the event when a fund is selected.
   * @param event - The event object containing the selected fund information.
   */
  OnFundSelected = (event) => {
    this.FundId = event.fundID;
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    this.EnableDownload = true;
    this.ProgressCancel = true;
    this.IsValidFundName = false;
    this.encryptedFundId = this.FundsList.filter(
      (s) => s.fundID === this.FundId
    )[0].encryptedFundId;

    if (!this.encryptedFundId) {
      this.hideUnauthorized = false;
    } else {
      this.hideUnauthorized = true;
    }
  };
  /**
   * Handles the event when an investor is selected.
   * @param event The event object containing the selected investor information.
   */
  OnInvestorSelected = (event) => {
    this.investorId = event.investorId;
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    this.EnableDownload = true;
    this.ProgressCancel = true;
    this.IsValidInvestor = false;
    this.encryptInvestorId = this.investorList.filter(
      (s) => s.investorId === this.investorId
    )[0].encryptedInvestorId;

    if (!this.encryptInvestorId) {
      this.hideUnauthorized = false;
    } else {
      this.hideUnauthorized = true;
    }
  };

  getSubFeatureAccessPermissions(companyId: string, featureId: number, moduleId: number)
  {
    this.loading = true;
    this.portfolioCompanyService.getSubFeatureAccessPermissions(companyId, featureId, moduleId).subscribe({
      next:(result) => {
        if(result.length > 0){
          this.canImport = result?.map(access => access.canImport).includes(true);
          this.loading = false;
        }
        else{
          this.canImport = false;
        }
      },
      error:(_error) => {
        this.loading = false;
      }
    });
  }

  onclickAllUpload(){
    if(this.canImport){
      this.showAllFileUploadPopUp = true;
    }
    else{
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    }
  }

  /**
   * Handles the selection of a company.
   * @param event - The event object containing the selected company's information.
   */
  CompanySelected(event) {
    if(this.kpiModuleAlias.List.includes(this.model.moduleDetails.alias)){
      let moduleId = 0;
      switch (this.model.moduleDetails.alias) {
        case this.kpiModuleAlias.Company:
          moduleId = this.kPIModulesEnum.Company;
          break;
        case this.kpiModuleAlias.TradingRecords:
          moduleId = this.kPIModulesEnum.TradingRecords;
          break;
        case this.kpiModuleAlias.Operational:
          moduleId = this.kPIModulesEnum.Operational;
          break;
        case this.kpiModuleAlias.Impact:
          moduleId = this.kPIModulesEnum.Impact;
          break;
        case this.kpiModuleAlias.Investment:
          moduleId = this.kPIModulesEnum.Investment;
          break;
        case this.kpiModuleAlias.Credit:
          moduleId = this.kPIModulesEnum.CreditKPI;
          break;
        case this.kpiModuleAlias.Financials:
          moduleId = this.kPIModulesEnum.ProfitAndLoss;
          break;
        case this.kpiModuleAlias.CapitalizationTable:
          moduleId = this.kPIModulesEnum.CapTable1;
          break;
        case this.kpiModuleAlias.OtherCapitalizationTable:
            moduleId = this.kPIModulesEnum.OtherCapTable1;
          break;
        case this.kpiModuleAlias.CustomTable1:
          moduleId = this.kPIModulesEnum.CustomTable1;
          break;
        case this.kpiModuleAlias.CustomTable2:
          moduleId = this.kPIModulesEnum.CustomTable2;
          break;
        case this.kpiModuleAlias.CustomTable3:
          moduleId = this.kPIModulesEnum.CustomTable3;
          break;
        case this.kpiModuleAlias.CustomTable4:
          moduleId = this.kPIModulesEnum.CustomTable4;
          break;
        case this.kpiModuleAlias.OtherKPI1:
          moduleId = this.kPIModulesEnum.OtherKPI1;
          break;
        case this.kpiModuleAlias.OtherKPI2:
          moduleId = this.kPIModulesEnum.OtherKPI2;
          break;
        case this.kpiModuleAlias.OtherKPI3:
          moduleId = this.kPIModulesEnum.OtherKPI3;
          break;
        case this.kpiModuleAlias.OtherKPI4:
          moduleId = this.kPIModulesEnum.OtherKPI4;
          break;
        case this.kpiModuleAlias.OtherKPI5:
          moduleId = this.kPIModulesEnum.OtherKPI5;
          break;
        case this.kpiModuleAlias.OtherKPI6:
          moduleId = this.kPIModulesEnum.OtherKPI6;
          break;
          case this.kpiModuleAlias.OtherKPI7:
          moduleId = this.kPIModulesEnum.OtherKPI7;
          break;
        case this.kpiModuleAlias.OtherKPI8:
          moduleId = this.kPIModulesEnum.OtherKPI8;
          break;
          case this.kpiModuleAlias.OtherKPI9:
          moduleId = this.kPIModulesEnum.OtherKPI9;
          break;
        case this.kpiModuleAlias.OtherKPI10:
          moduleId = this.kPIModulesEnum.OtherKPI10;
          break;
      }
      let pc = event.encryptedPortfolioCompanyId;
      this.getSubFeatureAccessPermissions(pc, FeaturesEnum.PortfolioCompany, moduleId);
    }
    this.PortfolioCompanyId = event.portfolioCompanyID;
    this.deleteiconclick();
    this.EnableDownload = true;
    this.ProgressCancel = true;
    this.uploadedFiles = [];
    this.IsValidCompany = false;
    this.encryptedPortfolioCompanyID = this.PorfolioCompanies.filter(
      (s) => s.portfolioCompanyID === this.PortfolioCompanyId
    )[0].encryptedPortfolioCompanyId;
    this.hideUnauthorized = this.permissionService.checkUserPermission(
      this.subFeature[this.featureName],
      this.actions[this.actions.canImport],
      this.encryptedPortfolioCompanyID
    );
    this.isPeriod = this.strModuleType == "Adhoc" ? true : false;
    if (this.strModuleType == "Adhoc") {
      this.clearData();
    }
  }

  /**
   * Handles the upload functionality.
   */
  onUpload() {
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    if (!this.CompanyDisabled && this.PortfolioCompanyId == undefined) {
      this.IsValidCompany = true;
    }
    if (!this.IsValidCompany) {
      for (let file of this.files) {
        this.uploadFiles(file);
      }
    }
  }

  /**
   * Handles the selection of files for upload.
   * @param files - The selected files.
   */
  onSelect(files: any) {
    this.files = files;
    this.uploadedFiles = [];
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    const validExcelExtensions = /\.(xlsx|xls)$/i;
    const errorMessages = [];
    if (!validExcelExtensions.test(files[0].name) && this.moduleName == "esg") {
      errorMessages.push("Please upload excel formats only");
    }
    const validZipExtensions = /\.(zip)$/i;
    if (
      !validZipExtensions.test(files[0].name) &&
      this.moduleName.toLocaleLowerCase() == this.fundOfFund.toLocaleLowerCase()
    ) {
      this.files = [];
      this.uploadFilePlaceholder = this.defaultPlaceholder;
      this.browseicon = true;
      this.toastrService.error("Please upload zip format file only", "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
      return;
    }
    if (files[0].size > 20000000) {
      errorMessages.push("File size exceeds the 20mb limit");
    }
    if (errorMessages.length > 0) {
      this.messageClass = "errorMessage";
      this.errormessages = [
        {
          isExpanded: false,
          messages: errorMessages,
        },
      ];
      this.errorCount = errorMessages.length;
    }
    if (!this.errormessages.length) {
      this.ProgressCancel = true;
      this.value = 0;
      this.cancel = false;
      this.FileProgresStatus = "Cancel File Progress";
      this.uploadFilePlaceholder = files[0].name;
      this.browseicon = false;
    }
  }
  /**
   * Cancels the upload process and resets the component state.
   * @param event - The event object.
   */
  onCancel(event: any) {
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
    this.value = 0;
    this.cancel = true;
    clearInterval(this.interval);
    this.ProgressCancel = true;
  }

  /**
   * Clears the selected files and resets the upload file placeholder, browse icon, messages, error messages, and message class.
   */
  deleteiconclick() {
    this.files = [];
    this.uploadFilePlaceholder = this.defaultPlaceholder;
    this.browseicon = true;
    this.messages = [];
    this.errormessages = [];
    this.messageClass = "";
  }

  /**
   * Downloads the template based on the selected module type and conditional dropdown value.
   */
  DownloadTemplate() {
    if(this.canImport){
      this.isLoading = true;
      this.messages = [];
      this.errormessages = [];
      this.messageClass = "";
  
      let res = {
        moduleType: this.strModuleType,
        EncryptedPortfolioCompanyID: null,
        EncryptedFundId: null,
        EncryptedInvestorId: null,
        FundId: this.selectedFundId,
        FundName: this.selectedFundName
      };
  
      if (this.isConditionalDropDown == "fund") {
        res.EncryptedFundId = this.encryptedFundId;
      } else if (this.isConditionalDropDown == "investor") {
        res.EncryptedInvestorId = this.encryptInvestorId;
      } else {
        res.EncryptedPortfolioCompanyID = this.encryptedPortfolioCompanyID;
      }
  
      if (this.isConditionalDropDown == this.fOFAliasName) {
        this.downloadFOFTemplate();
      } else {
        this.exportTemplate(res);
      }
    }
    else{
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: BulkuploadConstants.ToasterMessagePossition,
      });
    }
  }
  /**
   * Exports the template based on the provided response.
   * @param res The response object.
   */
  exportTemplate(res: any) {
    this.fileUploadService.exportTemplates(res).subscribe({
      next: (response) => {
        if (response.ok) {
          this.miscService.downloadExcelFile(response);
        } else {
          this.msgs = this.miscService.showAlertMessages(
            "error",
            "File is not downloaded."
          );
        }
        this.loading = false;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        const errorJson = JSON.parse(this.blobToString(error.error));
        this.loading = false;
        this.messageClass = "errorMessage";
        this.safeHtml = errorJson.message;
        if (this.isConditionalDropDown == "fund") {
          let msg = errorJson.message.toString().split("<br/>");
          let sheetName =
            errorJson.sheetName != undefined && errorJson.sheetName != null
              ? errorJson.sheetName.toString()
              : "Portfolio Company Level";
          let errorRes = {
            sheetname: sheetName,
            messages: msg,
            isExpanded: false,
          };
          this.errormessages.push(errorRes);
          this.errorCount = 1;
        }
        if (this.moduleName == BulkuploadConstants.MonthlyReportModule) {
          let msg = [errorJson.message?.toString()];
          let sheetName ="";
          let errorRes = {
            sheetname: sheetName,
            messages: msg,
            isExpanded: false,
          };
          this.errormessages.push(errorRes);
          this.errorCount = 1;
        }
        this.uploadResultobj.messageClass = this.messageClass;
        this.uploadResultobj.safeHtml = this.safeHtml;
        this.messages.push(this.uploadResultobj);
      },
    });
  }
  debounce(func: Function, wait: number): Function {
    return (...args: any[]) => {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => func.apply(this, args), wait);
    };
  }
  /**
   * Handles the change event of the fund selection.
   * @param event - The event object containing the selected fund(s).
   */
  onFundChangeModel = (event: any) => {
    this.errormessages = [];
    this.messageClass = "";
    this.hideUnauthorized = false;
    if(this.fundList?.length == this.FundsList?.length){
      this.isFundCheckAll = true;
    } else{
      this.isFundCheckAll = false;
    }
    if (this.fundList.length > 0) {
      this.hideUnauthorized = true;
    }
  };
  onFundKpisChangeModel = (event: any) => {
    this.errormessages = [];
    this.messageClass = "";
    if(event?.fundID > 0){
      this.selectedFundId = event.fundID;
      this.selectedFundName = event.fundName;
      this.hideUnauthorized = true;
    }else{
      this.selectedFundId = 0;
      this.selectedFundName = "";
      this.hideUnauthorized = false;
    }
  };
  /**
   * Downloads the FOFTemplate file.
   */
  downloadFOFTemplate() {
    this.isLoading = true;
    let fundIds = this.fundList.map(function (fund) {
      return fund.fundID;
    });
    let res = Object.assign({ moduleType: this.strModuleType, funds: fundIds });
    this.fileUploadService.foFExportTemplates(res).subscribe({
      next: (response) => {
        this.miscService.downloadZIPFile(response);
        this.toastrService.success("File downloaded successfully", "", {
          positionClass: BulkuploadConstants.ToasterMessagePossition,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.messageClass = "errorMessage";
        const errorJson = JSON.parse(this.blobToString(error.error));
        const copyErrorJson = errorJson;
        let errorCount = 0;
        copyErrorJson.forEach((item) => {
          let errorRes = {
            sheetname: item.sheetName,
            messages: [...item.excelCodeStatus],
            isExpanded: true,
          };
          this.errormessages.push(errorRes);
          errorCount = errorCount + item.excelCodeStatus.length;
        });
        this.errorCount = errorCount;
        this.isLoading = false;
      },
    });
  }
  /**
   * Converts a Blob object to a string.
   * @param {Blob} blob - The Blob object to convert.
   * @returns {string} - The converted string.
   */
  private blobToString(blob): string {
    const url = URL.createObjectURL(blob);
    let xmlRequest = new XMLHttpRequest();
    xmlRequest.open("GET", url, false);
    xmlRequest.send();
    URL.revokeObjectURL(url);
    return xmlRequest.response;
  }

  /**
   * Closes the error popup.
   */
  closeErrorPopup = () => {
    this.showErrorPopUp = false;
  };
  /**
   * Prepares the files list for upload.
   *
   * @param file - The file to be prepared.
   */
  prepareFilesList(file: any) {
    this.esgUpload(file);
  }
  /**
   * Prepares the list of files for FoF (Fund of Funds) bulk upload.
   *
   * @param file - The file to be processed.
   */
  prepareFoFFilesList(file: any) {
    this.cancel = true;
    this.FileProgresStatus = "File Processing...";
    this.messageClass = "";
    this.messages = [];
    let funds = this.fundList?.map((s) => s.fundID)?.join(",");
    const formData = new FormData();
    formData.append("FundIds", funds);
    formData.append("fileName", file.name);
    formData.append("formFile", file);
    this.fileUploadService.importFoFBulkData(formData).subscribe({
      next: (results: any) => {
        this.clearResponse();
      },
      error: (err) => {
        this.isLoading = false;
        this.clearResponse();
      },
    });
    this.isLoading = false;
    this.uploadService.notifyUploadCompleted();
  }
  /**
   * Clears the response and resets the state of the file upload component.
   */
  clearResponse() {
    this.files = [];
    this.uploadFilePlaceholder = this.defaultPlaceholder;
    this.browseicon = true;
    this.loading = false;
    this.errormessages = [];
    this.messageClass = "";
  }
  /**
   * Sets the errors for the bulk upload component.
   * Updates the message class, safe HTML, and messages array.
   * Resets the files, upload file placeholder, and browse icon.
   * Displays an error toast message.
   * Sets the loading flag to false.
   */
  setErrors() {
    this.messageClass = "errorMessage";
    this.uploadResultobj.messageClass = this.messageClass;
    this.uploadResultobj.safeHtml = this.safeHtml;
    this.messages.push(this.uploadResultobj);
    this.files = [];
    this.uploadFilePlaceholder = this.defaultPlaceholder;
    this.browseicon = true;
    this.toastrService.error(BulkuploadConstants.ErrorFoundSelectedFile, "", {
      positionClass: BulkuploadConstants.ToasterMessagePossition,
    });
    this.loading = false;
  }
  /**
   * Uploads the ESG file.
   *
   * @param file - The file to be uploaded.
   */
  esgUpload(file: any) {
    this.messageClass = "";
    this.messages = [];
    const formData = new FormData();
    formData.append("fileSize", file.size);
    formData.append("fileName", file.name);
    formData.append("formFile", file);
    formData.append(
      "encryptedPortfolioCompanyID",
      this.encryptedPortfolioCompanyID
    );
    formData.append("encryptedFundId", this.encryptedFundId);
    this.fileUploadService.UploadESG(formData).subscribe({
      next: (result: any) => {
        this.files = [];
        this.uploadFilePlaceholder = this.defaultPlaceholder;
        this.browseicon = true;
      },
      error: (err) => {
        const errorMsgs: any[] = (err.status == 400 && err?.error) || [];
        errorMsgs?.forEach((x) => {
          this.messages.push({
            messageClass: "",
            safeHtml: x.message,
          });
          this.ProgressCancel = true;
        });
      },
    });
    this.loading = false;
    this.uploadService.notifyUploadCompleted();
  }
}
