import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { DashboardConfigurationComponent } from './dashboard-configuration.component';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { ManageTrackerRecordsComponent } from 'src/app/components/dashboard-tracker/dashboard-configuration/manage-tracker-records/manage-tracker-records.component';
import { ManageTrackerFieldsComponent } from './manage-tracker-fields/manage-tracker-fields.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { DropDownsModule, ComboBoxModule, DropDownListModule } from '@progress/kendo-angular-dropdowns';
import { LabelModule } from "@progress/kendo-angular-label";
import { InputsModule } from '@progress/kendo-angular-inputs';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    DashboardConfigurationComponent,
    ManageTrackerFieldsComponent,
    ManageTrackerRecordsComponent
  ],
  imports: [
    CommonModule,
    SharedComponentModule,
    FormsModule,
    KendoModule,
    DropDownsModule,
    ComboBoxModule,
    DropDownListModule,
    LabelModule,
    InputsModule,
    RouterModule.forChild([
        { path: '', component: DashboardConfigurationComponent}
    ]),
],
  exports: [DashboardConfigurationComponent]
})
export class DashboardConfigurationModule {}
