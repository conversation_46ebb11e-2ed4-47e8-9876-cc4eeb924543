import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DashboardConfigurationComponent } from './dashboard-configuration.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DataAnalyticsConstants } from 'src/app/common/constants';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';

// Manually mock DataAnalyticsConstants for Jasmine
const mockConstants = {
  DashboardConfigurationTab: 'DashboardConfigurationTab',
  ManageTrackerFieldsTab: 'ManageTrackerFieldsTab',
  DeletedColumnTab: 'DeletedColumnTab',
  StatusFilterTab: 'StatusFilterTab',
};

// Assign mock constants before tests run
Object.assign(DataAnalyticsConstants, mockConstants);

describe('DashboardConfigurationComponent', () => {
  let component: DashboardConfigurationComponent;
  let fixture: ComponentFixture<DashboardConfigurationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DashboardConfigurationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set the first tab as selected by default', () => {
    expect(component.selectedTab).toBe(component.tabList[0]);
    expect(component.selectedTab.active).toBeTrue();
  });

  it('should activate the selected tab and deactivate others on tab click', () => {
    const tabToSelect: ITab = component.tabList[2];
    component.onTabClick(tabToSelect);
    expect(component.selectedTab).toBe(tabToSelect);
    component.tabList.forEach((tab, idx) => {
      if (idx === 2) {
        expect(tab.active).toBeTrue();
      } else {
        expect(tab.active).toBeFalse();
      }
    });
  });

  it('should have isDashboardConfigurationTab as true by default', () => {
    expect(component.isDashboardConfigurationTab).toBeTrue();
  });
});
