import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RepositoryConfigurationComponent } from './repository-configuration.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'; // Import CUSTOM_ELEMENTS_SCHEMA
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';

describe('RepositoryConfigurationComponent', () => {
  let component: RepositoryConfigurationComponent;
  let fixture: ComponentFixture<RepositoryConfigurationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [RepositoryConfigurationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: {} },
            params: of({}),
            queryParams: of({})
          }
        }
      ]
      // Add CUSTOM_ELEMENTS_SCHEMA
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RepositoryConfigurationComponent);
    component = fixture.componentInstance;
    component.tabs = [
      { name: 'Data Collection', active: true, hidden: false },
      { name: 'Email Notification', active: false, hidden: false }
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('onTabClick', () => {
    it('should set activeTab to the clicked tab name', () => {
      const clickedTab: ITab = { name: 'Email Notification', active: false, hidden: false };

      component.onTabClick(clickedTab);

      expect(component.activeTab).toBe('Email Notification');
    });

    it('should set active property to true for clicked tab', () => {

      const clickedTab: ITab = { name: 'Email Notification', active: false, hidden: false };

      component.onTabClick(clickedTab);

      const updatedTab = component.tabs.find(t => t.name === 'Email Notification');
      expect(updatedTab?.active).toBe(true);
    });

    it('should set active property to false for all other tabs', () => {

      const clickedTab: ITab = { name: 'Email Notification', active: false, hidden: false };

      component.onTabClick(clickedTab);

      const unselectedTab = component.tabs.find(t => t.name === 'Data Collection');
      expect(unselectedTab?.active).toBe(false);
    });

    it('should handle click on already active tab', () => {

      const clickedTab: ITab = { name: 'Data Collection', active: true, hidden: false };

      component.onTabClick(clickedTab);

      expect(component.activeTab).toBe('Data Collection');
      expect(component.tabs[0].active).toBe(true);
      expect(component.tabs[1].active).toBe(false);
    });

    it('should handle click when tabs array is empty', () => {

      component.tabs = [];
      const clickedTab: ITab = { name: 'Email Notification', active: false, hidden: false };

      component.onTabClick(clickedTab);

      expect(component.activeTab).toBe('Email Notification');

    });

    it('should maintain hidden property of tabs', () => {

      component.tabs = [
        { name: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', active: false, hidden: true }
      ];
      const clickedTab: ITab = { name: 'Data Collection', active: true, hidden: false };

      component.onTabClick(clickedTab);

      expect(component.tabs[1].hidden).toBe(true);
    });

    it('should update url to #/repository-configuration if activeTab is in url and active tab is Data Collection', () => {
      component.params = { activeTab: 'Data Collection' };
      spyOn(window.history, 'replaceState');
      const tab: ITab = { name: 'Data Collection', active: true, hidden: false };

      component.onTabClick(tab);

      expect(window.history.replaceState).toHaveBeenCalledWith({}, '', '#/repository-configuration');
    });
  });

  describe('onFundSelected', () => {
    it('should set selectedFunds to the provided funds array', () => {
      const funds = [{ id: 1, name: 'Fund 1' }, { id: 2, name: 'Fund 2' }];
      component.onFundSelected(funds);
      expect(component.selectedFunds).toEqual(funds);
    });
  });

  describe('onIsFundChange', () => {
    it('should set isFund to the provided boolean value', () => {
      component.isFund = false;
      component.onIsFundChange(true);
      expect(component.isFund).toBe(true);
      component.onIsFundChange(false);
      expect(component.isFund).toBe(false);
    });
  });
});

describe('RepositoryConfigurationComponent Additional Tests', () => {
  let component: RepositoryConfigurationComponent;
  let fixture: ComponentFixture<RepositoryConfigurationComponent>;
  let routerSpy: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    await TestBed.configureTestingModule({
      declarations: [RepositoryConfigurationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        { provide: Router, useValue: routerSpy },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: of({}),
          }
        }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(RepositoryConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should set selectedCompanies when onCompanySelected is called', () => {
    const companies = [{ id: 1, name: 'Company 1' }];
    component.onCompanySelected(companies);
    expect(component.selectedCompanies).toEqual(companies);
  });

  it('should handle onCompanySelected with empty array', () => {
    component.onCompanySelected([]);
    expect(component.selectedCompanies).toEqual([]);
  });

  it('should handle onCompanySelected with undefined', () => {
    component.onCompanySelected(undefined as any);
    expect(component.selectedCompanies).toBeUndefined();
  });

  it('should navigate to /email-configuration when navigateToEmailConfig is called', () => {
    component.navigateToEmailConfig();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/email-configuration']);
  });

  it('should initialize activeTab and tabs correctly', () => {
    expect(component.activeTab).toBe('Repository Configuration');
    expect(component.tabs.length).toBeGreaterThan(0);
    expect(component.tabs[0].active).toBeTrue();
  });

  it('should update activeTab and tabs when query param activeTab is present in ngOnInit', () => {
    const route = TestBed.inject(ActivatedRoute);
    (route.queryParams as any) = of({ activeTab: 'Email Notification' });
    component.ngOnInit();
    expect(component.activeTab).toBe('Email Notification');
    expect(component.tabs[1].active).toBeTrue();
  });

  it('should handle onTabClick with a tab not present in tabs', () => {
    const fakeTab: ITab = { name: 'Nonexistent Tab', active: false, hidden: false };
    component.onTabClick(fakeTab);
    expect(component.activeTab).toBe('Nonexistent Tab');
    // No tab in tabs should be active
    expect(component.tabs.every(t => !t.active)).toBeTrue();
  });

  it('should handle onFundSelected with undefined', () => {
    component.onFundSelected(undefined as any);
    expect(component.selectedFunds).toBeUndefined();
  });

  it('should handle onIsFundChange with undefined', () => {
    component.onIsFundChange(undefined as any);
    expect(component.isFund).toBeUndefined();
  });

  it('should handle missing queryParams in ngOnInit', () => {
    const route = TestBed.inject(ActivatedRoute);
    (route.queryParams as any) = of({});
    component.ngOnInit();
    expect(component.activeTab).toBe('Repository Configuration');
  });
});