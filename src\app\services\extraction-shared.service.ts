import { HttpClient, HttpHeaders, HttpErrorResponse } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { throwError, of } from "rxjs";
import { catchError, map, retry, concatMap, delay } from "rxjs/operators";
import { ExtractionUiConstants } from "../components/data-extraction/extraction-constants";
import { OidcAuthService } from "./oidc-auth.service";
@Injectable()
export class ExtractionSharedService {
  hostURL: string = "";
  config:any;
  constructor(
    private _http: HttpClient,
    @Inject("apiGatewayBaseUrl") baseUrl: string,
    private readonly oidcAuthService:OidcAuthService
  ) {
    this.hostURL = baseUrl;
    this.config=  this.oidcAuthService.getEnvironmentConfig();
  }
  // Helper method to get auth token
  private getAuthHeaders(): HttpHeaders {
    const token = this.oidcAuthService.getToken();
    return new HttpHeaders().set('Authorization', `Bearer ${token}`);
  }
  
  // Custom retry operator for 401 responses
  private retry401() {
    let retryCount = 0;
    const maxRetries = 3;
    
    return retry({
      count: maxRetries,
      delay: (error, retryCount) => {
        if (error.status === 401 && retryCount <= maxRetries) {
          console.log(`Retrying after 401 error. Attempt ${retryCount} of ${maxRetries}`);
          return of(error).pipe(delay(1000));
        }
        return throwError(() => error);
      }
    });
  }
  
  // GET method
  get(url: string) {
    const headers = this.getAuthHeaders();
    return this._http.get<any>(this.hostURL + url,{headers}).pipe(
      this.retry401(),
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  
  // POST method
  post(url: string, data: any) {
    const headers = this.getAuthHeaders();
    return this._http.post<any>(this.hostURL + url, data,{headers}).pipe(
      this.retry401(),
      map((response) => response),
      catchError(this.errorHandler)
    );
  }    // GET method with API key header
    getWithApiKey(url: string, appName: string=ExtractionUiConstants.API_Name) {
      const headers = new HttpHeaders()
      .set(ExtractionUiConstants.APP_Key, appName)
      return this._http.get<any>(this.hostURL + url, { headers }).pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
    }    
    // POST method with API key header
    postWithApiKey(url: string, data: any, appName: string= ExtractionUiConstants.API_Name) {
      const headers = new HttpHeaders()
      .set(ExtractionUiConstants.APP_Key, appName)
      return this._http.post<any>(this.hostURL + url, data, { headers }).pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
    }
  // Error handler
  errorHandler(error: any) {
    return throwError(error);
  }
}
