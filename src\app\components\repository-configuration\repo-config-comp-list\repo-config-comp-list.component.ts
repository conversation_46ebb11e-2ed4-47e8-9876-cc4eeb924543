import { Component, EventEmitter, Output, Input } from '@angular/core';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

@Component({
  selector: 'app-repository-configuration-company-list',
  templateUrl: './repo-config-comp-list.component.html',
  styleUrls: ['./repo-config-comp-list.component.scss']
})

export class RepositoryConfigurationCompanyListComponent {
  resetInProgress: boolean = false;
  @Output() selectedCompanies = new EventEmitter<any>();
  @Output() selectedFunds = new EventEmitter<any>();
  @Output() isFundChange = new EventEmitter<boolean>();
  @Input() preSelectedCompanies: any[] = []; // Add
  @Input() showSelectionContainer: boolean = true;  
  searchTerm: string = '';
  inputValue: string = '';
  selectAll: boolean = false;
  companies: { name: string, selected: boolean, companyId: string }[] = [];
  searchedCompanies: { name: string, selected: boolean }[] = [];
  isLoader: boolean = false;
  debounceTimeout: any;
  funds: { fundName: string, selected: boolean, fundID: string }[] = [];
  showFunds: boolean = false;

  constructor(
    private repositoryConfigService: RepositoryConfigService,
  ) { }

  ngOnInit() {
    this.getFundsAndPcs();
    this.repositoryConfigService.resetInProgress$.subscribe((state: boolean) => {
      this.resetInProgress = state;
    });
  }

  getFundListData() {
    this.showFunds = true;
    this.isFundChange.emit(this.showFunds);
    this.selectedCompanies.emit(null);    
    this.isLoader = true;
    this.repositoryConfigService.getFunds().subscribe(
      (data: any) => {
        this.funds = data.map((fund: any) => ({
          fundId: fund.fundID,
          fundName: fund.fundName,
          selected: false
        }));
        this.isLoader = false;
      },
      (error: any) => {
        console.error('Error fetching fund list:', error);
        this.isLoader = false;
      }
    );
  }

  getFundsAndPcs() {
    this.showFunds = false;
    this.isFundChange.emit(this.showFunds);
    this.selectedFunds.emit(null);    
    this.isLoader = true;
    this.repositoryConfigService.getPortfolioCompanies().subscribe(
      (data: any) => {
        this.companies = data.map((company: any) => ({
          name: company.companyName,
          companyId: company.portfolioCompanyID,
          selected: false
        }));
        this.applyPreselections(); // Add this line to apply pre-selections after companies are loaded
        this.isLoader = false;
      },
      (error: any) => {
        console.error('Error fetching portfolio companies:', error);
        this.isLoader = false;
      }
    );
  }

  get filteredCompanies() {
    return this.companies.filter(company =>
      company.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  toggleSelectAll() {
    this.filteredCompanies.forEach(company => company.selected = this.selectAll);
    const selectedPortfolioCompanies = this.filteredCompanies.filter(company => company.selected);
    if (selectedPortfolioCompanies.length > 0) {
      this.selectedCompanies.emit(selectedPortfolioCompanies);
    } else {
      this.selectedCompanies.emit(null);
      this.repositoryConfigService.displayConfigurationConflict = false; // Set to false when deselected
    }
  }

  checkSelection() {
    this.selectAll = this.filteredCompanies.every(company => company.selected);
    const selectedPortfolioCompanies = this.companies.filter(company => company.selected);
    if (selectedPortfolioCompanies) {
      this.selectedCompanies.emit(selectedPortfolioCompanies);
    } else {
      this.selectedCompanies.emit(null);
    }
  }

  fundCheckSelection() {    
    const selectedFunds = this.funds.filter(fund => fund.selected);
    if (selectedFunds) {
      this.selectedFunds.emit(selectedFunds);
    } else {
      this.selectedFunds.emit(null);
    }
  }

  debounce(func: Function, wait: number): Function {
    return (...args: any[]) => {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value.trim().toLowerCase();
    this.searchedCompanies = this.searchTerm
      ? this.companies.filter(company => company.name.toLowerCase().includes(this.searchTerm))
      : [];
  }

  // Add a method to apply pre-selections after companies are loaded
  private applyPreselections() {
    if (this.preSelectedCompanies && this.preSelectedCompanies.length > 0) {
      this.companies.forEach(company => {
        company.selected = this.preSelectedCompanies.some(
          selected => selected.companyId.toString() === company.companyId.toString()
        );
      });
      // Emit initially selected companies
      const selectedPortfolioCompanies = this.companies.filter(company => company.selected);
      if (selectedPortfolioCompanies.length > 0) {
        this.selectedCompanies.emit(selectedPortfolioCompanies);
      }
    }
  }
  
  get selectedFundItems() {
    return this.funds ? this.funds.filter(f => f.selected) : [];
  }

  get nonSelectedFundItems() {
    return this.funds ? this.funds.filter(f => !f.selected) : [];
  }
}