<div class="tab-container" id="dashboard-configuration-container">
<!-- Tab-Header -->
<div class="row dashboard-tab-bar m-0">
    <div class="col-12 px-0 py-1 d-flex align-items-center" id="home-container">
        <div>
            <nep-tab [tabList]="tabList" (OnSelectTab)="onTabClick($event)"></nep-tab>
        </div>
        <div class="ml-auto" *ngIf="selectedTab.name != ManageTrackerFieldsTab">
            <app-kendo-button name="cancel" type="Secondary">Cancel</app-kendo-button>
            <app-kendo-button [passedClass]="'px-5 mx-3'" name="save" type="Primary">Save</app-kendo-button>
        </div>
    </div>
</div>
<!-- Tab-Body -->
<ng-container *ngIf="selectedTab.name == DashboardConfigurationTab">
    <app-dashboard-tracker [passedClass]="'configuration-dashboard'" [isDashboardConfigurationTab]="isDashboardConfigurationTab"></app-dashboard-tracker>
</ng-container>
<ng-container *ngIf="selectedTab.name == ManageTrackerFieldsTab">
    <app-manage-tracker-fields></app-manage-tracker-fields>
</ng-container>
<ng-container *ngIf="selectedTab.name == DeletedColumnTab">
    DeletedColumnTab
</ng-container>
<ng-container *ngIf="selectedTab.name == StatusFilterTab">
    StatusFilterTab
</ng-container>
</div>

