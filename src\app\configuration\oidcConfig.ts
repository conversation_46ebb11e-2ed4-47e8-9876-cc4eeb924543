export const OidcConfig = {
  localhost: {
    env: "dev",
    wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://test.beatapps.net/identity/test/beat/sts/",
    endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
    introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-localhost-client-id_app",
    redirect_uri: "http://localhost:4200/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-localhost-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1sb2NhbGhvc3QtY2xpZW50LWlkX3NlcnZpY2VzOjYyNTk3ODg2LTYyRTQtNDMwOC05Q0Y2LTZFOEFCNTJBQUYwMA==",
    extraQueryParams: {
      brand_logo_url: "http://localhost:4200/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "http://localhost:4200",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_localhost",
  },
  dev: [ {
    env: "dev",
    wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://test.beatapps.net/identity/test/beat/sts/",
    endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
    introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-dev-client-id",
    redirect_uri: "https://dev.beatapps.net/foliosure/dev/pod/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-dev-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/dev/pod/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1kZXYtY2xpZW50LWlkX3NlcnZpY2VzOjVFNUQyMUJCLUQ4RTQtNEQ2NC05MDI0LTlGMDc5MEI5MDRGOA==",
    extraQueryParams: {
      brand_logo_url: "https://dev.beatapps.net/foliosure/dev/pod/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://dev.beatapps.net/foliosure/dev/pod/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_dev"
  },
  {
    wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://test.beatapps.net/identity/test/beat/sts/",
    endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
    introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-b-pec-dev-client-id",
    redirect_uri: "https://dev.beatapps.net/foliosure/dev/pod-b/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-dev-podb-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/test/pod-b/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod-b",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy10ZXN0LWNsaWVudC1pZF9zZXJ2aWNlczpFRjI3NzU1Ri1FRkNFLTQ3MUItQkIzRS1GNTdFRDMwM0RGRTc=",
    extraQueryParams: {
      brand_logo_url: "https://dev.beatapps.net/foliosure/dev/pod-b/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://dev.beatapps.net/foliosure/dev/pod-b/app/#/",
      support_email: "<EMAIL>",
    },
  },
],
  feature: {
    env: "dev",
    wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://test.beatapps.net/identity/test/beat/sts/",
    endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
    introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-feature-client-id",
    redirect_uri: "https://dev.beatapps.net/foliosure/feature/pod/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-feature-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/feature/pod/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1mZWF0dXJlLWNsaWVudC1pZF9zZXJ2aWNlczpBODY4MjgyQS0wNTc1LTRFN0UtODg5NC1GNzJENDIwRTc4QTI=",
    extraQueryParams: {
      brand_logo_url: "https://dev.beatapps.net/foliosure/feature/pod/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://dev.beatapps.net/foliosure/feature/pod/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_feature"
  },
  test: [
    {
      env: "test",
      wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
      authority: "https://test.beatapps.net/identity/test/beat/sts/",
      endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
      introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
      client_id: "beat-foliosure-pod-pec-test-client-id",
      redirect_uri: "https://test.beatapps.net/foliosure/test/pod/app/#/in",
      response_type: "code",
      scope: "openid profile offline_access beat-foliosure-pod-pec-test-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
      loadUserInfo: true,
      silent_redirect_uri: `${document.location.origin}/foliosure/test/pod/app/#/refresh`,
      automaticSilentRenew: true,
      clientName: "pod",
      introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy10ZXN0LWNsaWVudC1pZF9zZXJ2aWNlczpFRjI3NzU1Ri1FRkNFLTQ3MUItQkIzRS1GNTdFRDMwM0RGRTc=",
      extraQueryParams: {
        brand_logo_url: "https://test.beatapps.net/foliosure/test/pod/app/assets/dist/images/acuity-logo-full.svg",
        return_to_url: "https://test.beatapps.net/foliosure/test/pod/app/#/",
        support_email: "<EMAIL>",
      },
      client_env: "foliosure_test"
    },
    {
      wrapper_endpoint: "https://test.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
      authority: "https://test.beatapps.net/identity/test/beat/sts/",
      endsession_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/endsession",
      introspect_endpoint: "https://test.beatapps.net/identity/test/beat/sts/connect/introspect",
      client_id: "beat-foliosure-pod-pec-test-podb-client-id",
      redirect_uri: "https://test.beatapps.net/foliosure/test/pod-b/app/#/in",
      response_type: "code",
      scope: "openid profile offline_access beat-foliosure-pod-pec-test-podb-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
      loadUserInfo: true,
      silent_redirect_uri: `${document.location.origin}/foliosure/test/pod-b/app/#/refresh`,
      automaticSilentRenew: true,
      clientName: "pod-b",
      introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy10ZXN0LWNsaWVudC1pZF9zZXJ2aWNlczpFRjI3NzU1Ri1FRkNFLTQ3MUItQkIzRS1GNTdFRDMwM0RGRTc=",
      extraQueryParams: {
        brand_logo_url: "https://test.beatapps.net/foliosure/test/pod-b/app/assets/dist/images/acuity-logo-full.svg",
        return_to_url: "https://test.beatapps.net/foliosure/test/pod-b/app/#/",
        support_email: "<EMAIL>",
      },
      client_env: "foliosure_test"
    }
  ],
  perf1: {
    env: "perf",
    wrapper_endpoint: "https://uat.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://perf-test01.beatapps.net/identity/perf1/pod/sts/",
    endsession_endpoint: "https://perf-test01.beatapps.net/identity/perf1/pod/sts/connect/endsession",
    introspect_endpoint: "https://perf-test01.beatapps.net/identity/perf1/pod/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-perf1-client-id",
    redirect_uri: "https://perf-test01.beatapps.net/foliosure/perf1/pod/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-perf-client-id_services",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/perf1/pod/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1wZXJmLWNsaWVudC1pZF9zZXJ2aWNlczpCRDE3QTQxNC1DMjVCLTQwMkItODNEQi01QThBOTQ3Q0I0QUE=",
    extraQueryParams: {
      brand_logo_url: "https://perf-test01.beatapps.net/foliosure/perf1/pod/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://perf-test01.beatapps.net/foliosure/perf1/pod/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_perf"
  },
  perf2: {
    env: "perf",
    wrapper_endpoint: "https://uat.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://perf-test02.beatapps.net/identity/perf2/pod/sts/",
    endsession_endpoint: "https://perf-test02.beatapps.net/identity/perf2/pod/sts/connect/endsession",
    introspect_endpoint: "https://perf-test02.beatapps.net/identity/perf2/pod/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-perf2-client-id",
    redirect_uri: "https://perf-test02.beatapps.net/foliosure/perf2/pod/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-perf-client-id_services",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/perf2/pod/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1wZXJmLWNsaWVudC1pZF9zZXJ2aWNlczpCRDE3QTQxNC1DMjVCLTQwMkItODNEQi01QThBOTQ3Q0I0QUE=",
    extraQueryParams: {
      brand_logo_url: "https://perf-test02.beatapps.net/foliosure/perf2/pod/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://perf-test02.beatapps.net/foliosure/perf2/pod/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_perf"
  },
  uat: [{
    env: "uat",
    wrapper_endpoint: "https://uat.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://uat.beatapps.net/identity/uat/beat/sts/",
    endsession_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/endsession",
    introspect_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-uat-client-id",
    redirect_uri: "https://uat.beatapps.net/foliosure/uat/pod-a/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-uat-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/uat/pod-a/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod-a",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy11YXQtY2xpZW50LWlkX3NlcnZpY2VzOkREMTJGMTNGLTE1REItNDNBMC04NjVELUM0NTEzOUNEM0JFMg==",
    extraQueryParams: {
      brand_logo_url: "https://uat.beatapps.net/foliosure/uat/pod-a/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://uat.beatapps.net/foliosure/uat/pod-a/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_uat"
  },
  {
    wrapper_endpoint: "https://uat.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://uat.beatapps.net/identity/uat/beat/sts/",
    endsession_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/endsession",
    introspect_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-uat-podb-client-id",
    redirect_uri: "https://uat.beatapps.net/foliosure/uat/pod-b/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-uat-podb-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/uat/pod-b/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "pod-b",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy11YXQtY2xpZW50LWlkX3NlcnZpY2VzOkREMTJGMTNGLTE1REItNDNBMC04NjVELUM0NTEzOUNEM0JFMg==",
    extraQueryParams: {
      brand_logo_url: "https://uat.beatapps.net/foliosure/uat/pod-b/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://uat.beatapps.net/foliosure/uat/pod-b/app/#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_uat"
  },
  {
    wrapper_endpoint: "https://uat.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
    authority: "https://uat.beatapps.net/identity/uat/beat/sts/",
    endsession_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/endsession",
    introspect_endpoint: "https://uat.beatapps.net/identity/uat/beat/sts/connect/introspect",
    client_id: "beat-foliosure-pod-pec-uat-security-client-id",
    redirect_uri: "https://uat.beatapps.net/foliosure/uat/security/app/#/in",
    response_type: "code",
    scope: "openid profile offline_access beat-foliosure-pod-pec-uat-security-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
    loadUserInfo: true,
    silent_redirect_uri: `${document.location.origin}/foliosure/uat/security/app/#/refresh`,
    automaticSilentRenew: true,
    clientName: "security",
    introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy11YXQtY2xpZW50LWlkX3NlcnZpY2VzOkREMTJGMTNGLTE1REItNDNBMC04NjVELUM0NTEzOUNEM0JFMg==",
    extraQueryParams: {
      brand_logo_url: "https://uat.beatapps.net/foliosure/uat/security/app/assets/dist/images/acuity-logo-full.svg",
      return_to_url: "https://uat.beatapps.net/foliosure/uat/security/app#/",
      support_email: "<EMAIL>",
    },
    client_env: "foliosure_uat"
  }],
  security: [
    {
      wrapper_endpoint: "https://security.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
      authority: "https://security.beatapps.net/identity/security/pod/sts/",
      endsession_endpoint: "https://security.beatapps.net/identity/security/pod/sts/connect/endsession",
      introspect_endpoint: "https://security.beatapps.net/identity/security/pod/sts/connect/introspect",
      client_id: "beat-foliosure-pod-pec-security-client-id",
      redirect_uri: "https://security.beatapps.net/foliosure/security/pod/app/#/in",
      response_type: "code",
      scope: "openid profile offline_access beat-foliosure-pod-pec-security-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
      loadUserInfo: true,
      silent_redirect_uri: `${document.location.origin}/foliosure/security/pod/app/#/refresh`,
      automaticSilentRenew: true,
      clientName: "pod",
      introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy11YXQtY2xpZW50LWlkX3NlcnZpY2VzOkREMTJGMTNGLTE1REItNDNBMC04NjVELUM0NTEzOUNEM0JFMg==",
      extraQueryParams: {
        brand_logo_url: "https://security.beatapps.net/foliosure/security/pod/app/assets/dist/images/acuity-logo-full.svg",
        return_to_url: "https://security.beatapps.net/foliosure/security/pod/app/#/",
        support_email: "<EMAIL>",
          },
          client_env: "foliosure_security"
    },
    {
      wrapper_endpoint: "https://security.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
      authority: "https://security.beatapps.net/identity/security/pod/sts/",
      endsession_endpoint: "https://security.beatapps.net/identity/security/pod/sts/connect/endsession",
      introspect_endpoint: "https://security.beatapps.net/identity/security/pod/sts/connect/introspect",
      client_id: "beat-foliosure-pod-b-pec-security-client-id",
      redirect_uri: "https://security.beatapps.net/foliosure/security/pod-b/app/#/in",
      response_type: "code",
      scope: "openid profile offline_access beat-foliosure-pod-b-pec-security-client-id_services beat-ids-wrapper-api-pod-resource-id auth-admin-client-id_api",
      loadUserInfo: true,
      silent_redirect_uri: `${document.location.origin}/foliosure/security/pod-b/app/#/refresh`,
      automaticSilentRenew: true,
      clientName: "pod-b",
      introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy11YXQtY2xpZW50LWlkX3NlcnZpY2VzOkREMTJGMTNGLTE1REItNDNBMC04NjVELUM0NTEzOUNEM0JFMg==",      
      extraQueryParams: {
        brand_logo_url: "https://security.beatapps.net/foliosure/security/pod-b/app/assets/dist/images/acuity-logo-full.svg",
        return_to_url: "https://security.beatapps.net/foliosure/security/pod-b/app/#/",
        support_email: "<EMAIL>",
      }
    }
  ],
  prod:
    [
      {
        env: "prod",
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://demo.identity.beat-services.net/sts/",
        endsession_endpoint: "https://demo.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://demo.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-demo-client-id",
        redirect_uri: "https://demo.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-demo-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://demo.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "demo",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1kZW1vLWNsaWVudC1pZF9zZXJ2aWNlczpCOUQ5MjM2Qi0zMjg1LTRDRTgtQTdEMS1FNDUyRDU4RDQzQjA=",
        extraQueryParams: {
          brand_logo_url: "https://demo.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://demo.beatfoliosure.com/app#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_demo"
      },
       {
        env: "prod",
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://demo.identity.beat-services.net/sts/",
        endsession_endpoint: "https://demo.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://demo.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-demo2-client-id",
        redirect_uri: "https://demo2.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-demo2-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://demo2.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "demo2",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1kZW1vLWNsaWVudC1pZF9zZXJ2aWNlczpCOUQ5MjM2Qi0zMjg1LTRDRTgtQTdEMS1FNDUyRDU4RDQzQjA=",
        extraQueryParams: {
          brand_logo_url: "https://demo2.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://demo2.beatfoliosure.com/app#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_demo2"
      },
      {
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://identity.beat-services.net/sts/",
        endsession_endpoint: "https://identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-taabo-ch-client-id",
        redirect_uri: "https://taabo-ch.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-taaboch-prod-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://taabo-ch.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "taabo-ch",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://taabo-ch.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://taabo-ch.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_taabo-ch"
      },
      {
        wrapper_endpoint: "https://bristol.identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://bristol.identity.beat-services.net/sts/",
        endsession_endpoint: "https://bristol.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://bristol.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-bristol-client-id",
        redirect_uri: "https://bristol.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-bristol-prod-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://bristol.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "bristol",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://bristol.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://bristol.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_bristol"
      },
      {
        wrapper_endpoint: "https://admont.identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://admont.identity.beat-services.net/sts/",
        endsession_endpoint: "https://admont.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://admont.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-admont-client-id",
        redirect_uri: "https://admont.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-admont-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://admont.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "admont",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://admont.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://admont.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_admont"
      },
      {
        wrapper_endpoint: "https://asmt.identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://asmt.identity.beat-services.net/sts/",
        endsession_endpoint: "https://asmt.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://asmt.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-asmt-client-id",
        redirect_uri: "https://asmt.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-asmt-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://asmt.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "asmt",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://asmt.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://asmt.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_asmt"
      },
      {
        wrapper_endpoint: "https://monmouth.identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://monmouth.identity.beat-services.net/sts/",
        endsession_endpoint: "https://monmouth.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://monmouth.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-monmouth-client-id",
        redirect_uri: "https://monmouth.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-monmouth-prod-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://monmouth.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "monmouth",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://monmouth.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://monmouth.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_monmouth"
      },
      {
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://identity.beat-services.net/sts/",
        endsession_endpoint: "https://identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-exeter-client-id",
        redirect_uri: "https://exeter.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-exeter-prod-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://exeter.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "exeter",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://exeter.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://exeter.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_exeter"
      },
      {
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://demo.identity.beat-services.net/sts/",
        endsession_endpoint: "https://demo.identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://demo.identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-trial-client-id",
        redirect_uri: "https://trial.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-trial-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://trial.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "trial",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1kZW1vLWNsaWVudC1pZF9zZXJ2aWNlczpCOUQ5MjM2Qi0zMjg1LTRDRTgtQTdEMS1FNDUyRDU4RDQzQjA=",
        extraQueryParams: {
          brand_logo_url: "https://trial.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://trial.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_trial"
      },
      {
        wrapper_endpoint: "https://identity.beatapps.net/IdSWrapper/pod/IdsWrapperServices/api/",
        authority: "https://identity.beat-services.net/sts/",
        endsession_endpoint: "https://identity.beat-services.net/sts/connect/endsession",
        introspect_endpoint: "https://identity.beat-services.net/sts/connect/introspect",
        client_id: "beat-foliosure-pod-pec-himera-client-id",
        redirect_uri: "https://himera-staging.beatfoliosure.com/app/#/in",
        response_type: "code",
        scope: "openid profile offline_access beat-foliosure-pod-pec-himera-client-id_services",
        loadUserInfo: true,
        silent_redirect_uri: "https://himera-staging.beatfoliosure.com/app/#/refresh",
        automaticSilentRenew: true,
        clientName: "himera",
        introspectHeader: "YmVhdC1mb2xpb3N1cmUtcG9kLXBlYy1lbnNoaS1jbGllbnQtaWRfc2VydmljZXM6RTk4Qzk0QkQtOUEzMi00RUYxLTlFNzktQUY0OUY0NzVCNDJD",
        extraQueryParams: {
          brand_logo_url: "https://himera-staging.beatfoliosure.com/app/assets/dist/images/acuity-logo-full.svg",
          return_to_url: "https://himera-staging.beatfoliosure.com/app/#/",
          support_email: "<EMAIL>",
        },
        client_env: "foliosure_himera"
      },
    ],
  isIdsEnabled: true,
  applicationName: "BEAT FolioSure"
}