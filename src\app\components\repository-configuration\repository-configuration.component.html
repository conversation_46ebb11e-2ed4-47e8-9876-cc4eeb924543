<div class="repository-config-container">
  <div class="header-container d-flex justify-content-between align-items-center">
    <div class="tab-container">
      <nep-tab id="neptab" class="repository-tabs" [tabList]="tabs" (OnSelectTab)="onTabClick($event)">
      </nep-tab>
    </div>
    <div class="button-container">
      <button id="btn-email-notification" kendoButton class="email-config-btn" fillMode="outline" themeColor="primary"
        (click)="navigateToEmailConfig()">
        <img className="email-config-settings" src="assets/dist/images/email-settings.svg" alt="Email Notification" />
        User Configuration
      </button>
    </div>
  </div>
  <div class="content-container mt-3">
    <ng-container *ngIf="activeTab === 'Data Collection'"> 
      <div class="row">
        <div class="col-md-3 pr-0 content-height">
          <app-repository-configuration-company-list
            (selectedCompanies)="onCompanySelected($event)"
            (selectedFunds)="onFundSelected($event)"
            (isFundChange)="onIsFundChange($event)"></app-repository-configuration-company-list>
        </div>
        <div class="col-md-9 pl-4">
          <app-repository-configuration-doc-type
            [selectedCompanies]="selectedCompanies"
            [selectedFunds]="selectedFunds"
            [isFund]="isFund"></app-repository-configuration-doc-type>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="activeTab === 'Email Notification'">
      <div class="row">
        <div class="col-md-12">
          <app-email-notification [selectedCompanies]="selectedCompanies">
          </app-email-notification>
        </div>
      </div>
    </ng-container>
  </div>
</div>