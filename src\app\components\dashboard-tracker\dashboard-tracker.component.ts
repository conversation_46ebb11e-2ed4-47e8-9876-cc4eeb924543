import { Component, OnInit, Input } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { Observable, of } from 'rxjs';
import { ColGroupComponent, GridDataResult } from '@progress/kendo-angular-grid';
import { State } from '@progress/kendo-data-query';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridColumns: any[] = [];
  totalRecords: number = 0;
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;

  constructor(
    private dashboardTrackerService: DashboardTrackerService,
  ) {}

  ngOnInit(): void {
    this.loadDashboardTableData(this.state);
  }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    this.dashboardTrackerService.getDashboardTableData(filter).subscribe((response) => {
      if (response && response.data && response.columns) {
        this.gridColumns = response.columns;
        this.totalRecords = response.totalRecords || 0;
        this.view = of<GridDataResult>({
          data: response.data,
          total: this.totalRecords
        });        
      } else {
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
      this.isLoading = false;
    });
  }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

  navigateToDashboardConfig(): void {
    
  }
}
